# SourceRecycle Website Replica

A faithful replica of the SourceRecycle.com website built with modern HTML5, CSS3, and vanilla JavaScript.

## Features

- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Modern Layout**: Clean, professional design matching the original website
- **Interactive Elements**: 
  - Mobile-friendly navigation menu
  - Contact form with validation
  - Newsletter subscription
  - Smooth scrolling animations
  - Image hover effects
- **Accessibility**: Keyboard navigation support and semantic HTML
- **Performance Optimized**: Lazy loading images and debounced scroll events

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # CSS styling and responsive design
├── script.js           # JavaScript functionality
└── README.md           # Project documentation
```

## Technologies Used

- **HTML5**: Semantic markup and modern structure
- **CSS3**: Flexbox, Grid, animations, and responsive design
- **JavaScript (ES6+)**: Modern vanilla JavaScript for interactivity
- **Google Fonts**: Inter font family for typography

## Key Sections

1. **Header**: Fixed navigation with logo and menu
2. **Hero Section**: Main banner with call-to-action
3. **About Section**: Information about SourceRecycle solutions
4. **Mission Section**: Company vision and goals
5. **Empower Section**: Three-column layout with images and descriptions
6. **Contact Form**: Functional contact form with validation
7. **Solutions Gallery**: Image grid showcasing recycling solutions
8. **Footer**: Contact information, social links, and newsletter signup

## How to Run

1. **Simple HTTP Server** (recommended):
   ```bash
   python3 -m http.server 8000
   ```
   Then open `http://localhost:8000` in your browser

2. **Alternative servers**:
   ```bash
   # Node.js
   npx serve .
   
   # PHP
   php -S localhost:8000
   ```

3. **Direct File Opening**: You can also open `index.html` directly in your browser, though some features may not work due to CORS restrictions.

## Features Implemented

### Responsive Design
- Mobile-first approach
- Breakpoints at 768px and 480px
- Collapsible mobile navigation
- Flexible grid layouts

### Interactive Elements
- Mobile hamburger menu
- Form validation and submission
- Smooth scroll animations
- Image hover effects
- Loading animations on scroll

### Performance Features
- Optimized images with proper sizing
- Debounced scroll events
- Lazy loading for images
- Efficient CSS animations

## Browser Compatibility

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Design Matching

The replica closely matches the original website including:
- Color scheme (green theme with #4CAF50 primary color)
- Typography (Inter font family)
- Layout and spacing
- Image placement and sizing
- Interactive elements and animations
- Responsive behavior

## Future Enhancements

- Add more advanced animations
- Implement a backend for form submissions
- Add more interactive features
- Optimize for better performance
- Add more accessibility features

## License

This is a replica created for educational purposes. All content and images belong to their respective owners.

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
  --primary-green: #43a047;
  --primary-green-dark: #2e7031;
  --primary-green-light: #81c784;
  --accent-green: #b9f6ca;
  --surface: #fff;
  --background: #f4f7f5;
  --text-main: #222;
  --text-secondary: #4b6043;
  --shadow: 0 2px 8px rgba(67,160,71,0.10), 0 1.5px 6px rgba(67,160,71,0.08);
  --radius: 14px;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-main);
    background-color: var(--background);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: var(--surface);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.nav-logo {
    display: flex;
    align-items: center;
    min-width: 150px;
}

.logo {
    height: 50px;
    width: auto;
    max-width: 200px;
    display: block;
    transition: opacity 0.3s ease;
}

.logo:hover {
    opacity: 0.8;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
}

.nav-link {
    text-decoration: none;
    color: var(--text-main);
    font-weight: 500;
    transition: color 0.3s ease;
    font-size: 1.08rem;
    letter-spacing: 0.01em;
    padding: 0 2px 6px 2px;
}

.nav-link:hover {
    color: var(--primary-green);
}

.nav-link.active {
    border-bottom: 2.5px solid var(--primary-green);
    color: var(--primary-green-dark);
    padding-bottom: 2px;
    font-weight: 500;
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: #fff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 8px;
    padding: 10px 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
    list-style: none;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 10px 20px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.3s ease;
}

.dropdown-link:hover {
    background-color: #f5f5f5;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-green-light) 0%, var(--primary-green) 100%);
    color: #fff;
    padding: 110px 0 60px;
    margin-top: 80px;
    border-radius: 0 0 var(--radius) var(--radius);
    box-shadow: var(--shadow);
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-content {
    text-align: center;
    margin-bottom: 60px;
}

.hero-title {
    font-size: 3.2rem;
    font-weight: 700;
    margin-bottom: 18px;
    line-height: 1.15;
    letter-spacing: -1px;
}

.hero-title br {
    display: block;
}

.hero-subtitle {
    font-size: 1.18rem;
    margin-bottom: 28px;
    opacity: 0.95;
}

.hero-cta {
    display: inline-block;
    background: var(--surface);
    color: var(--primary-green-dark);
    border: 2px solid var(--primary-green);
    padding: 12px 36px;
    border-radius: var(--radius);
    font-weight: 600;
    font-size: 1.1rem;
    text-decoration: none;
    transition: background 0.2s, color 0.2s;
    box-shadow: var(--shadow);
}

.hero-cta:hover {
    background: var(--primary-green);
    color: #fff;
    border-color: var(--primary-green-dark);
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-top: 60px;
}

.feature-card {
    background: var(--surface);
    color: var(--text-main);
    padding: 36px 18px 28px 18px;
    border-radius: var(--radius);
    min-width: 320px;
    text-align: center;
    box-shadow: var(--shadow);
    border: none;
}

.feature-card h3 {
    font-size: 1.18rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--primary-green-dark);
}

.feature-card p {
    font-size: 1.05rem;
    margin-bottom: 0;
}

@media (max-width: 900px) {
    .hero-features {
        flex-direction: column;
        gap: 18px;
        align-items: center;
    }
    .feature-card {
        min-width: 0;
        width: 100%;
    }
}

/* Main Content Sections */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 80px 0;
}

section {
    margin-bottom: 80px;
}

h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: #2E7D32;
    text-align: center;
}

p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Image Gallery */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.gallery-item img:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Empower Section */
.empower-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.empower-item {
    text-align: center;
    padding: 30px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
    background: var(--accent-green);
}

.empower-item:hover {
    transform: translateY(-5px);
}

.empower-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 20px;
}

.empower-item h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--primary-green-dark);
}

/* Contact Form */
.contact-section {
    background: var(--background);
    padding: 80px 0;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
    background: var(--surface);
    padding: 40px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border-radius: var(--radius);
    border: 1.5px solid var(--primary-green-light);
    background: #f8faf8;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-green);
}

.submit-btn {
    background: var(--primary-green);
    color: #fff;
    padding: 15px 30px;
    border: none;
    border-radius: var(--radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    width: 100%;
    box-shadow: var(--shadow);
}

.submit-btn:hover {
    background: var(--primary-green-dark);
    transform: translateY(-2px);
}

/* Solutions Grid */
.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.solution-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: var(--radius);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: var(--shadow);
}

.solution-item img:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Footer Revamp */
.footer {
    background: var(--primary-green-dark);
    color: #fff;
    padding: 48px 0 0 0;
    border-radius: var(--radius) var(--radius) 0 0;
    box-shadow: var(--shadow);
    margin-bottom: 0;
}

.footer-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 32px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    gap: 32px;
    min-height: 220px;
    padding-bottom: 0;
}

.footer-col {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 220px;
    flex: 1 1 0;
    height: 100%;
}

.footer-brand-col {
    align-items: flex-start;
    min-width: 260px;
    max-width: 320px;
}

.footer-brand-main {
    align-items: flex-start;
}

.footer-brand h3 {
    font-size: 1.5rem;
    margin-bottom: 8px;
    font-weight: 700;
}

.footer-brand p {
    margin-bottom: 8px;
    font-size: 1rem;
}

.social-links {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
}

.social-link svg {
    width: 24px;
    height: 24px;
    fill: #fff;
    display: block;
    transition: fill 0.2s;
}

.social-link {
    background: var(--primary-green-light);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
    color: var(--primary-green-dark);
}

.social-link:hover {
    background: var(--primary-green);
    color: #fff;
}

.social-link i,
.fab,
.fas,
.far,
.fal,
.fad {
  font-family: 'Font Awesome 6 Free' !important;
  font-weight: 900 !important;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

.loyalty-btn {
    margin-top: 8px;
    background: var(--primary-green-dark);
    color: #fff;
    border: none;
    border-radius: var(--radius);
    padding: 10px 28px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 8px;
    transition: background 0.2s;
    text-decoration: none;
    display: inline-block;
    box-shadow: var(--shadow);
}

.loyalty-btn:hover {
    background: var(--primary-green);
    color: #fff;
}

.footer-copyright {
    font-size: 0.98rem;
    color: #e0e0e0;
    align-self: flex-start;
    margin-top: 24px;
}

.footer-contact-col {
    align-items: center;
    justify-content: center;
    text-align: center;
    min-width: 200px;
}

.footer-newsletter-col {
    align-items: flex-end;
    min-width: 260px;
    max-width: 320px;
}

.footer-newsletter-main {
    width: 100%;
    align-items: flex-end;
    text-align: right;
}

.footer-contact h4,
.footer-newsletter h4 {
    font-size: 1.08rem;
    margin-bottom: 6px;
    font-weight: 600;
}

.footer-contact-details {
    font-size: 1rem;
    color: #fff;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.newsletter-form input[type="email"] {
    padding: 13px 16px;
    border-radius: 10px;
    border: none;
    font-size: 1rem;
    margin-bottom: 0;
}

.newsletter-form button {
    background: #4CAF50;
    color: #fff;
    border: none;
    border-radius: 22px;
    padding: 12px 0;
    font-size: 1.08rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 4px;
    transition: background 0.2s;
}

.newsletter-form button:hover {
    background: #388e3c;
}

.footer-links-row {
    display: flex;
    gap: 24px;
    margin-top: 18px;
    justify-content: flex-end;
    width: 100%;
}

.footer-link {
    color: #fff;
    text-decoration: none;
    font-size: 1rem;
    transition: color 0.2s;
}

.footer-link:hover {
    color: var(--accent-green);
}

.delete-link {
    color: #e53935;
    font-weight: 500;
}

.delete-link:hover {
    color: #ff1744;
}

@media (max-width: 900px) {
    .footer-content {
        flex-direction: column;
        gap: 32px;
        align-items: stretch;
        min-height: 0;
    }
    .footer-col {
        min-width: 0;
        max-width: 100%;
        height: auto;
    }
    .footer-newsletter-main, .footer-links-row {
        align-items: flex-start;
        text-align: left;
        justify-content: flex-start;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 20px 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 15px 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-features {
        grid-template-columns: 1fr;
    }

    h2 {
        font-size: 2rem;
    }

    .image-gallery {
        grid-template-columns: 1fr;
    }

    .empower-grid {
        grid-template-columns: 1fr;
    }

    .solutions-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-cta {
        padding: 12px 25px;
        font-size: 1rem;
    }

    .feature-card {
        padding: 20px;
    }

    .contact-form {
        padding: 30px 20px;
    }

    .solutions-grid {
        grid-template-columns: 1fr;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.loading {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Image Error Handling */
img {
    transition: opacity 0.3s ease;
}

img[src*="data:image/svg+xml"] {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
}

/* Accessibility Improvements */
.social-link:focus,
.nav-link:focus,
.hero-cta:focus,
.submit-btn:focus {
    outline: 2px solid #4CAF50;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .social-links {
        display: none;
    }

    .hero {
        background: white;
        color: black;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-30 {
    margin-bottom: 30px;
}

.mt-50 {
    margin-top: 50px;
}

/* Animation for better UX */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content {
    animation: fadeInUp 0.8s ease-out;
}

.feature-card {
    animation: fadeInUp 0.8s ease-out;
    animation-delay: 0.2s;
    animation-fill-mode: both;
}

/* About Section Revamp */
.about-section {
    margin-bottom: 80px;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1.1fr;
    gap: 48px;
    align-items: center;
    margin-bottom: 36px;
}

.about-text h2 {
    color: #222;
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 24px;
    text-align: left;
}

.about-text p {
    color: #444;
    font-size: 1.18rem;
    line-height: 1.7;
    text-align: left;
    margin-bottom: 0;
    max-width: 600px;
}

/* About Section Images Grouping */
.about-images {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 18px;
}

.about-main-image img {
    width: 420px;
    height: 240px;
    object-fit: cover;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
}

.about-gallery {
    display: flex;
    gap: 18px;
    margin-top: 0;
}

.about-gallery-item img {
    width: 180px;
    height: 110px;
    object-fit: cover;
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.07);
}

/* Mission Section Image */
.mission-image {
    display: flex;
    justify-content: center;
    margin-top: 32px;
}
.mission-image img {
    width: 420px;
    height: 240px;
    object-fit: cover;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
}

@media (max-width: 1100px) {
    .about-grid {
        grid-template-columns: 1fr;
        gap: 32px;
    }
    .about-images {
        align-items: flex-start;
    }
    .about-main-image img,
    .mission-image img {
        width: 100%;
        height: 180px;
    }
    .about-gallery {
        flex-direction: row;
        gap: 12px;
    }
    .about-gallery-item img {
        width: 48vw;
        max-width: 180px;
        height: 80px;
    }
}

.lazy-img {
  filter: blur(8px);
  opacity: 0;
  transition: filter 0.7s cubic-bezier(.4,0,.2,1), opacity 0.7s cubic-bezier(.4,0,.2,1);
}
.lazy-img.loaded {
  filter: blur(0);
  opacity: 1;
}
